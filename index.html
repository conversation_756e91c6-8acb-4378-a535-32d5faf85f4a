<!DOCTYPE html>
<html lang="pt-br" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>To<PERSON> da <PERSON> | Sal <PERSON></title>
    <!-- Inclui o Tailwind CSS para componentes -->
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.css" rel="stylesheet" />
    <style>
        /* Define a fonte Inter para todo o corpo do documento */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f7f7f7;
            color: #4b5563; /* cor do texto */
        }
        /* Estilo para a barra de navegação flutuante com "glassmorphism" melhorado */
        .sticky-nav {
            background-color: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(20px) saturate(180%);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Estado scrolled da navbar */
        .navbar-scrolled {
            background-color: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px) saturate(200%);
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
        }

        /* Animações para links da navbar */
        .nav-link {
            position: relative;
            transition: all 0.3s ease;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            width: 0;
            height: 2px;
            bottom: -4px;
            left: 50%;
            background: linear-gradient(90deg, #4c6c51, #6a8c6a);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-link:hover::after {
            width: 100%;
        }
        /* Ajuste para o fundo do herói com a imagem de fundo verde */
        .hero-background {
            background-image: url('./background.jpg');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }

        /* Animação suave para elementos do hero */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.8s ease-out forwards;
        }

        /* Gradiente para melhor legibilidade */
        .text-gradient {
            background: linear-gradient(135deg, #10b981, #34d399);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        /* Classe para um efeito de sombra suave e arredondada */
        .card-shadow {
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08); /* Sombra mais sutil */
        }
        /* Estilo para a imagem do produto, ajustando para telas pequenas */
        .product-image {
            max-width: 100%;
            height: auto;
        }
        @media (min-width: 768px) {
            .product-image {
                max-width: 70%;
            }
        }
    </style>
    <!-- Adicione a fonte Inter do Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="font-sans antialiased text-gray-700">

<!-- Barra de Navegação Flutuante com design moderno -->
<nav id="navbar" class="fixed w-full z-50 top-0 sticky-nav">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16 md:h-20">
            <!-- Logo da marca -->
            <div class="flex-shrink-0">
                <a href="#hero" class="flex items-center space-x-3 group">
                    <div class="relative">
                        <img src="https://placehold.co/140x45/d7e0d3/4c6c51?text=Toque+da+Terra"
                             class="h-8 md:h-10 transition-transform duration-300 group-hover:scale-105"
                             alt="Logo Toque da Terra">
                        <!-- Glow effect on hover -->
                        <div class="absolute inset-0 bg-[#4c6c51] opacity-0 group-hover:opacity-20 rounded-lg blur-xl transition-opacity duration-300"></div>
                    </div>
                </a>
            </div>

            <!-- Menu Desktop -->
            <div class="hidden md:block">
                <div class="ml-10 flex items-baseline space-x-8">
                    <a href="#historia" class="nav-link text-gray-700 hover:text-[#4c6c51] px-3 py-2 text-sm font-medium transition-colors duration-300">
                        Nossa História
                    </a>
                    <a href="#beneficios" class="nav-link text-gray-700 hover:text-[#4c6c51] px-3 py-2 text-sm font-medium transition-colors duration-300">
                        Benefícios
                    </a>
                    <a href="#depoimentos" class="nav-link text-gray-700 hover:text-[#4c6c51] px-3 py-2 text-sm font-medium transition-colors duration-300">
                        Depoimentos
                    </a>
                    <a href="#contato" class="nav-link text-gray-700 hover:text-[#4c6c51] px-3 py-2 text-sm font-medium transition-colors duration-300">
                        Contato
                    </a>
                </div>
            </div>

            <!-- CTA Button e Mobile Menu -->
            <div class="flex items-center space-x-4">
                <!-- CTA Button -->
                <a href="#comprar" class="group relative inline-flex items-center justify-center px-6 py-2.5 text-sm font-semibold text-white bg-[#4c6c51] rounded-full transition-all duration-300 hover:bg-[#3d5a42] hover:scale-105 shadow-lg hover:shadow-xl">
                    <span class="relative z-10">Comprar Sal Mirtz</span>
                    <div class="absolute inset-0 bg-gradient-to-r from-[#4c6c51] to-[#6a8c6a] rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    <!-- Shine effect -->
                    <div class="absolute inset-0 bg-white opacity-0 group-hover:opacity-20 rounded-full transform scale-0 group-hover:scale-100 transition-all duration-500"></div>
                </a>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button type="button"
                            id="mobile-menu-button"
                            class="relative inline-flex items-center justify-center p-2 rounded-full text-gray-600 hover:text-[#4c6c51] hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-[#4c6c51] transition-all duration-300"
                            aria-controls="mobile-menu"
                            aria-expanded="false">
                        <span class="sr-only">Abrir menu principal</span>
                        <!-- Hamburger icon -->
                        <svg class="block h-6 w-6 transition-transform duration-300" id="menu-icon-open" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                        <!-- Close icon -->
                        <svg class="hidden h-6 w-6 transition-transform duration-300" id="menu-icon-close" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Menu -->
    <div class="md:hidden hidden" id="mobile-menu">
        <div class="px-2 pt-2 pb-3 space-y-1 bg-white bg-opacity-95 backdrop-blur-lg border-t border-gray-200 shadow-lg">
            <a href="#historia" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-700 hover:text-[#4c6c51] hover:bg-gray-50 rounded-lg transition-all duration-300">
                Nossa História
            </a>
            <a href="#beneficios" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-700 hover:text-[#4c6c51] hover:bg-gray-50 rounded-lg transition-all duration-300">
                Benefícios
            </a>
            <a href="#depoimentos" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-700 hover:text-[#4c6c51] hover:bg-gray-50 rounded-lg transition-all duration-300">
                Depoimentos
            </a>
            <a href="#contato" class="mobile-nav-link block px-3 py-2 text-base font-medium text-gray-700 hover:text-[#4c6c51] hover:bg-gray-50 rounded-lg transition-all duration-300">
                Contato
            </a>
        </div>
    </div>
</nav>

<!-- Seção do Herói (Hero Section) - Primeira tela de impacto -->
<section id="hero" class="relative hero-background min-h-screen flex items-center justify-center overflow-hidden">
    <!-- Overlay para melhor legibilidade do texto -->
    <div class="absolute inset-0 bg-black bg-opacity-40"></div>

    <!-- Container principal do conteúdo -->
    <div class="relative z-10 container mx-auto px-4 md:px-8 lg:px-12">
        <div class="max-w-4xl mx-auto text-center text-white">
            <!-- Badge/Tag superior -->
            <div class="inline-flex items-center px-4 py-2 mb-6 text-sm font-medium text-[#4c6c51] bg-white bg-opacity-90 backdrop-blur-sm rounded-full shadow-lg">
                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                </svg>
                100% Natural e Artesanal
            </div>

            <!-- Título principal otimizado -->
            <h1 class="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold tracking-tight mb-6 leading-tight">
                <span class="block">Cozinhar é um</span>
                <span class="block text-transparent bg-clip-text bg-gradient-to-r from-green-300 to-green-100">
                    ato de amor
                </span>
            </h1>

            <!-- Subtítulo melhorado -->
            <p class="text-lg md:text-xl lg:text-2xl mb-8 max-w-3xl mx-auto font-light leading-relaxed text-gray-100">
                Mas e quando a rotina te impede de vivê-lo com calma?
                <span class="block mt-2 font-medium text-white">
                    A gente sabe que falta de tempo não pode ser sinônimo de falta de sabor.
                </span>
            </p>

            <!-- Botões de ação -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-8">
                <a href="#comprar" class="group inline-flex items-center justify-center px-8 py-4 text-base font-semibold text-white bg-[#4c6c51] rounded-full transition-all duration-300 transform hover:scale-105 hover:bg-[#3d5a42] shadow-xl hover:shadow-2xl">
                    <span>Descubra o Sal Mirtz</span>
                    <svg class="w-5 h-5 ml-2 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                    </svg>
                </a>
                <a href="#historia" class="inline-flex items-center justify-center px-8 py-4 text-base font-medium text-white border-2 border-white border-opacity-50 rounded-full transition-all duration-300 hover:bg-white hover:text-[#4c6c51] backdrop-blur-sm">
                    Nossa História
                </a>
            </div>

            <!-- Indicadores de benefícios -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12 max-w-3xl mx-auto">
                <div class="flex items-center justify-center space-x-3 text-white bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4">
                    <div class="flex-shrink-0 w-8 h-8 bg-[#4c6c51] rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <span class="text-sm font-medium">Menos tempo, mais sabor</span>
                </div>
                <div class="flex items-center justify-center space-x-3 text-white bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4">
                    <div class="flex-shrink-0 w-8 h-8 bg-[#4c6c51] rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <span class="text-sm font-medium">100% Natural</span>
                </div>
                <div class="flex items-center justify-center space-x-3 text-white bg-white bg-opacity-10 backdrop-blur-sm rounded-lg p-4">
                    <div class="flex-shrink-0 w-8 h-8 bg-[#4c6c51] rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                        </svg>
                    </div>
                    <span class="text-sm font-medium">Receita familiar</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Scroll indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <a href="#historia" class="flex flex-col items-center text-white opacity-70 hover:opacity-100 transition-opacity">
            <span class="text-xs mb-2">Role para baixo</span>
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
            </svg>
        </a>
    </div>
</section>

<!-- Seção Sobre o Sal Mirtz e a História da Marca -->
<section id="historia" class="py-16 md:py-24 bg-gray-50">
    <div class="container mx-auto px-4 md:px-8">
        <div class="flex flex-col md:flex-row items-center gap-12">
            <!-- Imagem do produto e storytelling visual, com foco em um design clean -->
            <div class="md:w-1/2 flex justify-center items-center">
                <img src="https://placehold.co/500x500/f4f4f4/4c6c51?text=Sal+Mirtz+-+O+Toque+Final" alt="Embalagem do Sal Mirtz" class="rounded-3xl card-shadow product-image">
            </div>
            <!-- Texto da história com linguagem acolhedora -->
            <div class="md:w-1/2">
                <h2 class="text-3xl md:text-4xl font-bold mb-4 text-[#4c6c51]">
                    O sabor que abraça, feito para você.
                </h2>
                <p class="text-lg mb-4 leading-relaxed">
                    Você sabe que cozinhar não é só alimentar. É criar lembranças, unir a família à mesa e aquecer corações a cada garfada.
                </p>
                <p class="text-lg mb-4 leading-relaxed">
                    Na cozinha da Dona Miriam, lá em Tupanciretã-RS, também era assim. E foi ali que nasceu mais que uma receita: nasceu um gesto de amor que atravessou gerações. O que começou como um tempero caseiro, aprimorado com paciência por mais de 10 anos, hoje é a essência da Toque da Terra.
                </p>
                <p class="text-lg mb-4 leading-relaxed">
                    Nós estamos aqui para apoiar você, que cozinha para sua família, ajudando a transformar a rotina em momentos únicos, de afeto e celebração. Chegamos para facilitar o seu trabalho e dar o toque final para que cada refeição seja tão especial quanto as memórias que você cria com quem ama.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Seção de Benefícios - Resolvendo as dores do dia a dia -->
<section id="beneficios" class="py-16 md:py-24">
    <div class="container mx-auto px-4 md:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 text-[#4c6c51]">
            Seu segredo para uma cozinha mais leve.
        </h2>
        <p class="text-lg mb-12 max-w-3xl mx-auto">
            O Sal Mirtz foi feito para você que busca praticidade sem abrir mão de um sabor autêntico.
        </p>
        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Cards de benefício com design mais clean e sombra suave -->
            <div class="bg-white p-8 rounded-3xl card-shadow transition-transform transform hover:scale-105">
                <h3 class="text-xl font-semibold mb-2 text-[#4c6c51]">Menos tempo, mais sabor</h3>
                <p class="text-sm text-gray-600">
                    Cansada de perder horas na cozinha? O Sal Mirtz é a solução pronta que te salva na correria, transformando pratos simples em refeições dignas de elogios.
                </p>
            </div>
            <div class="bg-white p-8 rounded-3xl card-shadow transition-transform transform hover:scale-105">
                <h3 class="text-xl font-semibold mb-2 text-[#4c6c51]">Adeus, "o que vamos comer?"</h3>
                <p class="text-sm text-gray-600">
                    Com a versatilidade do Sal Mirtz, a decisão se torna fácil. Use-o em qualquer prato e garanta um sabor delicioso, nutritivo e que a família vai amar.
                </p>
            </div>
            <div class="bg-white p-8 rounded-3xl card-shadow transition-transform transform hover:scale-105">
                <h3 class="text-xl font-semibold mb-2 text-[#4c6c51]">Sem culpa, só orgulho</h3>
                <p class="text-sm text-gray-600">
                    Nossos temperos são 100% naturais e artesanais. O Sal Mirtz é o atalho que te dá orgulho do resultado, sem a sensação de estar usando algo industrial.
                </p>
            </div>
            <div class="bg-white p-8 rounded-3xl card-shadow transition-transform transform hover:scale-105">
                <h3 class="text-xl font-semibold mb-2 text-[#4c6c51]">Para o seu tempero brilhar</h3>
                <p class="text-sm text-gray-600">
                    Sabe quando o prato fica tão bom que todo mundo elogia? O Sal Mirtz é o toque final que faz a mágica acontecer, garantindo que o seu esforço seja sempre notado.
                </p>
            </div>
            <div class="bg-white p-8 rounded-3xl card-shadow transition-transform transform hover:scale-105">
                <h3 class="text-xl font-semibold mb-2 text-[#4c6c51]">Um punhado de afeto</h3>
                <p class="text-sm text-gray-600">
                    Cozinhar é criar memórias afetivas. O Sal Mirtz é o ingrediente que traz de volta o sabor da infância e a sensação de aconchego da comida de mãe.
                </p>
            </div>
            <div class="bg-white p-8 rounded-3xl card-shadow transition-transform transform hover:scale-105">
                <h3 class="text-xl font-semibold mb-2 text-[#4c6c51]">Seu parceiro na rotina</h3>
                <p class="text-sm text-gray-600">
                    A vida moderna pede praticidade. A Toque da Terra é o apoio que você precisa para equilibrar todos os seus papéis, sem sacrificar a saúde e o sabor da sua família.
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Seção de Depoimentos (Opcional, mas adiciona prova social) -->
<section id="depoimentos" class="py-16 md:py-24 bg-gray-50">
    <div class="container mx-auto px-4 md:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 text-[#4c6c51]">
            Quem provou, aprovou!
        </h2>
        <p class="text-lg mb-12 max-w-3xl mx-auto">
            Veja o que as mulheres que cozinham com amor estão dizendo sobre o Sal Mirtz.
        </p>
        <div class="grid md:grid-cols-2 gap-8">
            <!-- Depoimentos com design mais clean e tipografia mais espaçada -->
            <div class="bg-white p-8 rounded-3xl card-shadow text-center">
                <img class="w-16 h-16 mx-auto mb-4 rounded-full" src="https://placehold.co/100x100/e0e0e0/000000?text=Usuária+1" alt="Foto de Perfil de uma Usuária">
                <p class="text-lg italic text-gray-600">
                    "O Sal Mirtz virou o meu segredo na cozinha. Ele dá um sabor incrível e me salva na correria do dia a dia."
                </p>
                <p class="mt-4 font-semibold text-[#4c6c51]">
                    Ana, 35 anos
                </p>
            </div>
            <div class="bg-white p-8 rounded-3xl card-shadow text-center">
                <img class="w-16 h-16 mx-auto mb-4 rounded-full" src="https://placehold.co/100x100/e0e0e0/000000?text=Usuária+2" alt="Foto de Perfil de uma Usuária">
                <p class="text-lg italic text-gray-600">
                    "Eu achava que não era boa na cozinha, mas o Sal Mirtz me deu a confiança que precisava. Minha família ama!"
                </p>
                <p class="mt-4 font-semibold text-[#4c6c51]">
                    Sofia, 42 anos
                </p>
            </div>
        </div>
    </div>
</section>

<!-- Seção do Produto e Compra -->
<section id="comprar" class="py-16 md:py-24 bg-[#4c6c51]">
    <div class="container mx-auto px-4 md:px-8 text-center text-white">
        <h2 class="text-3xl md:text-4xl font-bold mb-4">
            A essência do sabor na sua cozinha.
        </h2>
        <p class="text-lg mb-8 max-w-2xl mx-auto font-light">
            O Sal Mirtz foi feito para você que busca naturalidade, praticidade e afeto em cada refeição.
        </p>
        <div class="bg-white p-8 rounded-3xl card-shadow max-w-lg mx-auto flex flex-col items-center text-gray-800">
            <!-- Imagem do produto em um fundo claro, com destaque -->
            <img src="https://placehold.co/400x400/f4f4f4/4c6c51?text=Sal+Mirtz+Produto+Clean" alt="Produto Sal Mirtz" class="w-full md:w-2/3 mb-6 rounded-2xl">
            <h3 class="text-2xl font-bold mb-2">Sal Mirtz</h3>
            <p class="text-3xl font-extrabold text-[#4c6c51] mb-6">
                R$ 23,00
            </p>
            <p class="text-sm text-center mb-4">
                Temperos 100% naturais e artesanais. <br>Um toque da terra, um punhado de afeto, um sabor que abraça.
            </p>
            <!-- Botão de Compra com design minimalista e bordas arredondadas -->
            <a href="#" class="w-full inline-flex items-center justify-center px-8 py-4 text-base font-medium text-white bg-[#4c6c51] rounded-full transition-transform transform hover:scale-105">
                Quero o Sal Mirtz na minha cozinha
            </a>
        </div>
    </div>
</section>

<!-- Seção de Contato -->
<section id="contato" class="py-16 md:py-24">
    <div class="container mx-auto px-4 md:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold mb-4 text-[#4c6c51]">
            Vamos nos conectar!
        </h2>
        <p class="text-lg mb-8 max-w-2xl mx-auto">
            Sabor que nasce do cuidado. Ao seu lado, do preparo ao último sorriso à mesa.
        </p>
        <div class="flex flex-col md:flex-row justify-center items-center gap-8">
            <!-- Itens de contato com ícones e texto -->
            <div class="flex items-center gap-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-[#4c6c51]" viewBox="0 0 24 24" fill="currentColor"><path d="M7 2a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H7zm5 17a1 1 0 1 1 0-2 1 1 0 0 1 0 2z"/></svg>
                <p class="text-lg">+55 55 93619-6535</p>
            </div>
            <div class="flex items-center gap-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-[#4c6c51]" viewBox="0 0 24 24" fill="currentColor"><path d="M2.003 5.884L10 9.874l7.997-3.99A2 2 0 0 0 16 4H4a2 2 0 0 0-1.997 1.884zM4 8.76v8.448a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8.76l-8 3.99L4 8.76z"/></svg>
                <p class="text-lg"><EMAIL></p>
            </div>
            <div class="flex items-center gap-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-[#4c6c51]" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm3.2 2.8c1.17 0 2.12.95 2.12 2.12s-.95 2.12-2.12 2.12-2.12-.95-2.12-2.12.95-2.12 2.12-2.12zm-4.48 0c-1.17 0-2.12.95-2.12 2.12s.95 2.12 2.12 2.12 2.12-.95 2.12-2.12-.95-2.12-2.12-2.12zm0 4.48c-1.17 0-2.12.95-2.12 2.12s.95 2.12 2.12 2.12 2.12-.95 2.12-2.12-.95-2.12-2.12-2.12zm-3.2 0c-1.17 0-2.12.95-2.12 2.12s.95 2.12 2.12 2.12 2.12-.95 2.12-2.12-.95-2.12-2.12-2.12zM12 18a6 6 0 1 1 0-12 6 6 0 0 1 0 12zm0-2a4 4 0 1 0 0-8 4 4 0 0 0 0 8z"/></svg>
                <p class="text-lg">@toqueda.terra</p>
            </div>
        </div>
    </div>
</section>

<!-- Rodapé -->
<footer class="bg-gray-200 text-center py-6">
    <p class="text-sm text-gray-600">&copy; 2025 Toque da Terra Alimentos LTDA. Todos os direitos reservados.</p>
</footer>

<!-- Inclui o JavaScript do Flowbite para funcionalidades de componentes -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.js"></script>
<script>
    // Enhanced navbar scroll effects
    window.addEventListener('scroll', function() {
        const navbar = document.getElementById('navbar');
        const scrolled = window.scrollY > 50;

        if (scrolled) {
            navbar.classList.add('navbar-scrolled');
        } else {
            navbar.classList.remove('navbar-scrolled');
        }
    });

    // Mobile menu functionality
    document.addEventListener('DOMContentLoaded', function() {
        const mobileMenuButton = document.getElementById('mobile-menu-button');
        const mobileMenu = document.getElementById('mobile-menu');
        const menuIconOpen = document.getElementById('menu-icon-open');
        const menuIconClose = document.getElementById('menu-icon-close');
        const mobileNavLinks = document.querySelectorAll('.mobile-nav-link');

        // Toggle mobile menu
        mobileMenuButton.addEventListener('click', function() {
            const isExpanded = mobileMenuButton.getAttribute('aria-expanded') === 'true';

            // Toggle menu visibility
            mobileMenu.classList.toggle('hidden');

            // Toggle icons
            menuIconOpen.classList.toggle('hidden');
            menuIconClose.classList.toggle('hidden');

            // Update aria-expanded
            mobileMenuButton.setAttribute('aria-expanded', !isExpanded);

            // Add animation classes
            if (!isExpanded) {
                mobileMenu.classList.add('animate-fade-in-up');
            }
        });

        // Close mobile menu when clicking on a link
        mobileNavLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.add('hidden');
                menuIconOpen.classList.remove('hidden');
                menuIconClose.classList.add('hidden');
                mobileMenuButton.setAttribute('aria-expanded', 'false');
            });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const isClickInsideNav = navbar.contains(event.target);
            const isMenuVisible = !mobileMenu.classList.contains('hidden');

            if (!isClickInsideNav && isMenuVisible) {
                mobileMenu.classList.add('hidden');
                menuIconOpen.classList.remove('hidden');
                menuIconClose.classList.add('hidden');
                mobileMenuButton.setAttribute('aria-expanded', 'false');
            }
        });

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    const offsetTop = target.offsetTop - 80; // Account for fixed navbar
                    window.scrollTo({
                        top: offsetTop,
                        behavior: 'smooth'
                    });
                }
            });
        });
    });
</script>
</body>
</html>
